# Neutron `create_floatingip` 完整调用流程和继承层次结构分析

## 1. 调用流程分析

### 1.1 API 入口点

**REST API 入口**：
- URL: `POST /v2.0/floatingips`
- 通过 `neutron/extensions/l3.py` 中的 `L3` 扩展定义
- 使用 `resource_helper.build_resource_info()` 构建资源路由
- 映射到 L3 服务插件的 `create_floatingip` 方法

### 1.2 继承链中的方法解析顺序 (MRO)

```python
# 实际继承链
L3RouterPlugin
├── L3_HA_NAT_db_mixin (HA 支持)
├── L3_NAT_with_dvr_db_mixin (DVR 支持)  
├── L3_NAT_db_mixin (RPC 通知层)
├── L3_NAT_dbonly_mixin (核心数据库实现)
└── RouterPluginBase (抽象基类)
```

**方法解析顺序**：
1. `L3RouterPlugin.create_floatingip()` - 服务插件入口
2. `L3_NAT_with_dvr_db_mixin.create_floatingip()` - DVR 版本 (如果启用)
3. `L3_NAT_db_mixin.create_floatingip()` - RPC 通知层
4. `L3_NAT_dbonly_mixin.create_floatingip()` - 核心数据库实现
5. `L3_NAT_dbonly_mixin._create_floatingip()` - 实际创建逻辑

### 1.3 完整执行路径

#### 阶段 1: API 层处理
```
REST API Request → L3 Extension → L3RouterPlugin.create_floatingip()
```

#### 阶段 2: 服务插件处理
```python
# neutron/services/l3_router/l3_router_plugin.py
def create_floatingip(self, context, floatingip):
    return super(L3RouterPlugin, self).create_floatingip(
        context, floatingip,
        initial_status=n_const.FLOATINGIP_STATUS_DOWN)
```

#### 阶段 3: DVR 处理 (如果启用)
```python
# neutron/db/l3_dvr_db.py
def create_floatingip(self, context, floatingip, initial_status):
    floating_ip = self._create_floatingip(context, floatingip, initial_status)
    self._notify_floating_ip_change(context, floating_ip)  # DVR 特定通知
    return floating_ip
```

#### 阶段 4: RPC 通知层
```python
# neutron/db/l3_db.py - L3_NAT_db_mixin
def create_floatingip(self, context, floatingip, initial_status):
    floatingip_dict = super(L3_NAT_db_mixin, self).create_floatingip(
        context, floatingip, initial_status)
    router_id = floatingip_dict['router_id']
    self.notify_router_updated(context, router_id, 'create_floatingip')
    return floatingip_dict
```

#### 阶段 5: 核心数据库实现
```python
# neutron/db/l3_db.py - L3_NAT_dbonly_mixin
def create_floatingip(self, context, floatingip, initial_status):
    return self._create_floatingip(context, floatingip, initial_status)
```

### 1.4 核心创建逻辑 (`_create_floatingip`)

#### 步骤 1: 前置回调
```python
registry.publish(resources.FLOATING_IP, events.BEFORE_CREATE,
                 self, payload=events.DBEventPayload(
                     context, request_body=floatingip))
```

#### 步骤 2: 验证和准备
- 验证外部网络有效性
- 验证网络包含子网
- 生成 FloatingIP UUID

#### 步骤 3: 创建外部端口
```python
port = {'tenant_id': '',  # 系统端口，不属于租户
        'network_id': f_net_id,
        'admin_state_up': True,
        'device_id': 'PENDING',
        'device_owner': DEVICE_OWNER_FLOATINGIP,
        'status': constants.PORT_STATUS_NOTAPPLICABLE,
        'name': ''}

external_port = plugin_utils.create_port(
    self._core_plugin, context.elevated(),
    {'port': port}, check_allow_post=False)
```

#### 步骤 4: 数据库事务处理
```python
with plugin_utils.delete_port_on_error(...), \
     context.session.begin(subtransactions=True):
    
    # 创建 FloatingIP 对象
    floatingip_obj = l3_obj.FloatingIP(...)
    
    # 处理关联 (如果指定了 port_id)
    assoc_result = self._update_fip_assoc(...)
    
    # 保存到数据库
    floatingip_obj.create()
    
    # DNS 集成处理
    if self._is_dns_integration_supported:
        dns_data = self._process_dns_floatingip_create_precommit(...)
    
    # QoS 处理
    if self._is_fip_qos_supported:
        self._process_extra_fip_qos_create(...)
    
    # 预提交回调
    registry.notify(resources.FLOATING_IP, events.PRECOMMIT_CREATE, ...)
```

#### 步骤 5: 后置处理
```python
# 更新端口设备 ID
self._core_plugin.update_port(context.elevated(), external_port['id'],
                              {'port': {'device_id': fip_id}})

# 关联后回调
registry.notify(resources.FLOATING_IP, events.AFTER_UPDATE, ...)

# DNS 后置处理
if self._is_dns_integration_supported:
    self._process_dns_floatingip_create_postcommit(...)

# 扩展处理
resource_extend.apply_funcs(l3_apidef.FLOATINGIPS, floatingip_dict, ...)
```

## 2. 继承层次结构

### 2.1 抽象基类
```python
# neutron/extensions/l3.py
@six.add_metaclass(abc.ABCMeta)
class RouterPluginBase(object):
    @abc.abstractmethod
    def create_floatingip(self, context, floatingip):
        pass
```

### 2.2 核心数据库实现类
```python
# neutron/db/l3_db.py
class L3_NAT_dbonly_mixin(l3.RouterPluginBase):
    def create_floatingip(self, context, floatingip, initial_status):
        return self._create_floatingip(context, floatingip, initial_status)
    
    def _create_floatingip(self, context, floatingip, initial_status):
        # 核心实现逻辑
```

### 2.3 RPC 通知层
```python
# neutron/db/l3_db.py
class L3_NAT_db_mixin(L3_NAT_dbonly_mixin, L3RpcNotifierMixin):
    def create_floatingip(self, context, floatingip, initial_status):
        floatingip_dict = super(L3_NAT_db_mixin, self).create_floatingip(
            context, floatingip, initial_status)
        router_id = floatingip_dict['router_id']
        self.notify_router_updated(context, router_id, 'create_floatingip')
        return floatingip_dict
```

### 2.4 DVR 扩展
```python
# neutron/db/l3_dvr_db.py  
class L3_NAT_with_dvr_db_mixin(..., l3_db.L3_NAT_db_mixin):
    def create_floatingip(self, context, floatingip, initial_status):
        floating_ip = self._create_floatingip(context, floatingip, initial_status)
        self._notify_floating_ip_change(context, floating_ip)  # DVR 特定通知
        return floating_ip
```

### 2.5 HA 扩展
```python
# neutron/db/l3_hamode_db.py
class L3_HA_NAT_db_mixin(l3_dvr_db.L3_NAT_with_dvr_db_mixin):
    # 继承 DVR 实现，添加 HA 特定功能
```

### 2.6 服务插件
```python
# neutron/services/l3_router/l3_router_plugin.py
class L3RouterPlugin(L3_HA_NAT_db_mixin, ...):
    def create_floatingip(self, context, floatingip):
        return super(L3RouterPlugin, self).create_floatingip(
            context, floatingip,
            initial_status=n_const.FLOATINGIP_STATUS_DOWN)
```

## 3. 实现变体

### 3.1 Neutron 核心插件
- **L3_NAT_dbonly_mixin**: 核心数据库实现
- **L3_NAT_db_mixin**: RPC 通知层
- **L3_NAT_with_dvr_db_mixin**: DVR 分布式路由支持
- **L3_HA_NAT_db_mixin**: 高可用性支持

### 3.2 服务插件
- **L3RouterPlugin**: 主要的 L3 服务插件
- 支持多种扩展：DVR、HA、QoS、DNS 集成等

### 3.3 扩展模块
- **DNS 集成**: `dns_db.py` - 提供 DNS 域名解析
- **QoS 支持**: `l3_fip_qos.py` - 浮动 IP QoS 策略
- **端口转发**: `portforwarding/pf_plugin.py` - 端口转发功能
- **弹性 SNAT**: `elastic_snat/plugin.py` - 弹性 SNAT 功能

### 3.4 第三方驱动/插件
通过 `service_providers/driver_controller.py` 支持第三方实现

## 4. 集成点

### 4.1 回调系统集成
```python
# 主要回调事件
- BEFORE_CREATE: 创建前验证和预处理
- PRECOMMIT_CREATE: 数据库提交前处理
- AFTER_CREATE: 创建后通知和清理
- AFTER_UPDATE: 关联更新后处理
```

**关键回调订阅者**：
- **端口转发插件**: 检查端口冲突
- **弹性 SNAT 插件**: 防止冲突操作
- **计量插件**: 更新计量规则
- **Nova 通知器**: 通知计算服务
- **资源通知插件**: 外部系统通知

### 4.2 数据库模型关联
```python
# neutron/db/models/l3.py
class FloatingIP(model_base.BASEV2):
    floating_port_id = sa.ForeignKey('ports.id')  # 外部端口
    fixed_port_id = sa.ForeignKey('ports.id')     # 内部端口
    router_id = sa.ForeignKey('routers.id')       # 关联路由器
    
    # ORM 关系
    port = orm.relationship(models_v2.Port, ...)
    fixed_port = orm.relationship(models_v2.Port, ...)
    router = orm.relationship(Router, ...)
```

### 4.3 Agent 通信机制
```python
# L3 Agent RPC 通知
self.l3_rpc_notifier.routers_updated(
    context, [router_id], 'create_floatingip')

# DVR 特定通知
if is_distributed_router(router):
    self.l3_rpc_notifier.routers_updated_on_host(
        context, [router_id], host)
```

**通知类型**：
- `routers_updated`: 通用路由器更新
- `routers_updated_on_host`: 特定主机更新 (DVR)
- `router_added_to_agent`: 路由器添加到 Agent
- `router_removed_from_agent`: 路由器从 Agent 移除

### 4.4 外部服务通知
- **Nova**: 网络变更通知
- **DNS**: 域名记录更新
- **计量服务**: 流量计费更新
- **监控系统**: 资源状态变更

## 5. 数据库操作与事务处理

### 5.1 事务边界
```python
with context.session.begin(subtransactions=True):
    # 原子操作
    floatingip_obj.create()
    registry.notify(PRECOMMIT_CREATE, ...)
```

### 5.2 错误处理
```python
with plugin_utils.delete_port_on_error(
        self._core_plugin, context.elevated(), external_port['id']):
    # 如果出错，自动清理已创建的端口
```

### 5.3 并发控制
- 使用数据库约束防止重复分配
- `@db_api.retry_if_session_inactive()` 装饰器处理会话失效

## 6. 关键配置和扩展点

### 6.1 配置选项
- `enable_dvr`: 启用分布式虚拟路由
- `l3_ha`: 启用高可用性
- `dns-integration`: DNS 集成支持
- `qos-fip`: 浮动 IP QoS 支持

### 6.2 扩展点
- **回调系统**: 通过 `registry.receives` 注册处理器
- **资源扩展**: 通过 `resource_extend.extends` 添加属性
- **服务提供者**: 通过驱动控制器支持第三方实现

## 7. 性能和优化考虑

### 7.1 数据库优化
- 使用 ORM 关系的 `lazy='joined'` 减少查询次数
- 索引优化：外键和唯一约束
- 批量操作支持

### 7.2 缓存机制
- Agent 状态缓存
- 网络拓扑缓存
- DNS 记录缓存

### 7.3 异步处理
- RPC 异步通知
- 后台任务处理
- 定期清理任务

这个分析涵盖了 Neutron `create_floatingip` 的完整调用流程、继承结构、集成点和实现细节，为理解和维护这个复杂系统提供了全面的参考。